#ifndef KKONDK_TOUCH_H
#define KKONDK_TOUCH_H
#include <linux/input.h>
#include <linux/types.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <dirent.h>
#include <linux/uinput.h>
#include <iostream>
#include <cstdio>
#include <cstdlib>
#include <string>
#include <regex>
#include <sstream>
#include <thread>
#include <chrono>
#include "VecTool.h"
#include "../src/ImGuiTOOL/kma_driver.h"

// 计算两个三维向量之间的角度
VecTor2 FastAtan2(VecTor3 Enem, VecTor3 Self)
{
    VecTor2 AimCoordinates;
    float RotationX = Enem.x - Self.x;
    float RotationY = Enem.y - Self.y;
    float RotationZ = Enem.z - Self.z;
    float RotationH = sqrt((RotationX * RotationX) + (RotationY * RotationY));
    AimCoordinates.x = atan2(RotationZ, RotationH) * 57.29577951308f; // 转换为角度
    if (RotationX >= 1 && RotationY >= 1)
    {
        AimCoordinates.y = atan(RotationY / RotationX) * 57.29577951308f;
    }
    else if (RotationX >= 1 && RotationY <= 1)
    {
        AimCoordinates.y = atan(RotationY / RotationX) * 57.29577951308f;
    }
    else if (RotationX <= 1 && RotationY >= 1)
    {
        AimCoordinates.y = atan(RotationY / RotationX) * 57.29577951308f + 180;
    }
    else if (RotationX <= 1 && RotationY <= 1)
    {
        AimCoordinates.y = atan(RotationY / RotationX) * 57.29577951308f - 180;
    }
    return AimCoordinates;
}

// 定义一些宏用于位操作
#define test_bit(array, bit) ((array[bit / BITS_PER_LONG] >> bit % BITS_PER_LONG) & 1)
#define NBITS(x) ((((x) - 1) / BITS_PER_LONG) + 1)

// 触控命令存储数据结构
struct Touch
{
    int TRACKING_ID; // 追踪ID
    int POSITION_X;  // X坐标
    int POSITION_Y;  // Y坐标
    int SLOT;        // 槽位
    bool io;         // 输入输出标志
    bool Action;     // 动作标志
    bool run;        // 运行标志
    bool isbot;      // 是否为机器人
    bool runing;     // 正在运行标志
};

// 触控设备数据结构
struct eventinfo
{
    int ID;                    // 事件ID
    bool io = false;           // 输入输出标志
    struct input_absinfo data; // 绝对输入信息
};

// 触控设备数据结构
struct events
{
    int ID;                          // 事件ID
    bool io = false;                 // 输入输出标志
    bool infoio = false;             // 信息输入输出标志
    eventinfo eventmsg[KEY_MAX + 1]; // 事件信息
} eventdata[EV_MAX + 1];

// 触控类
class touch
{
private:
    bool mainio;                                              // 主输入输出标志
    int write_fb;                                             // 写入帧缓冲
    bool Aimio = false;                                       // 瞄准输入输出标志
    bool IsFire = false;                                      // 是否开火
    Timer MainAimiSleep;                                      // 主瞄准休眠计时器
    int FullScreenX = 0;                                      // 全屏X
    int FullScreenY = 0;                                      // 全屏Y
    bool AimAtIo = false;                                     // 瞄准输入输出标志
    VecTor2 TouchProportion;                                  // 触摸比例
    TOUCH_INFORMATION *touch_information = nullptr;           // 触摸信息
    RESOLUTION_INFORMATION *resolution_information = nullptr; // 分辨率信息
    Driver *kma_driver = nullptr;                             // kma驱动实例，用于自瞄触摸

    // 旋转点函数，根据屏幕方向调整坐标
    VecTor2 RotatePoint(float PointX, float PointY, VecTor2 Display)
    {
        VecTor2 Rotate = {0, 0};
        if (resolution_information->Orientation == 3)
        {
            Rotate.x = Display.y - PointY;
            Rotate.y = PointX;
        }
        else if (resolution_information->Orientation == 2)
        {
            Rotate.x = Display.x - PointX;
            Rotate.y = Display.y - PointY;
        }
        else if (resolution_information->Orientation == 1)
        {
            Rotate.x = PointY;
            Rotate.y = Display.x - PointX;
        }
        else if (resolution_information->Orientation == 0)
        {
            Rotate.x = PointX;
            Rotate.y = PointY;
        }
        return Rotate;
    }

    // 机器人触摸结构体
    struct BotTouch
    {
        VecTor2 endLU;                                            // 左上角结束点
        VecTor2 endRD;                                            // 右下角结束点
        VecTor2 mspeed;                                           // 移动速度
        VecTor2 maxMove;                                          // 最大移动
        VecTor2 AimMove;                                          // 瞄准移动
        VecTor2 Touchsize;                                        // 触摸尺寸
        VecTor2 nowPointer;                                       // 当前指针
        long long loadTime;                                       // 加载时间
        bool botio = false;                                       // 机器人输入输出标志
        VecTor2 beginPointer;                                     // 开始指针
        bool botstart = false;                                    // 机器人开始标志
        float ThreadSpeed = 0;                                    // 线程速度
        VecTor2 AimRot = {0, 0};                                  // 瞄准旋转
        VecTor2 *TouchProportion;                                 // 触摸比例
        TOUCH_INFORMATION *touch_information = nullptr;           // 触摸信息
        struct timespec strtime, endtime, Intervaltime;           // 时间结构
        RESOLUTION_INFORMATION *resolution_information = nullptr; // 分辨率信息

        // 构造函数，初始化触摸信息和分辨率信息
        BotTouch(TOUCH_INFORMATION *_touch_information, RESOLUTION_INFORMATION *_resolution_information, VecTor2 Touchsize_)
        {
            if (_touch_information != nullptr && _resolution_information != nullptr)
            {
                touch_information = _touch_information;
                resolution_information = _resolution_information;
            }
            clock_gettime(CLOCK_MONOTONIC, &strtime);
            Touchsize = Touchsize_;
            beginPointer.x = Touchsize.x * touch_information->TouchPoints.x;
            beginPointer.y = Touchsize.y * touch_information->TouchPoints.y;
            endLU.x = beginPointer.x - Touchsize.x * (touch_information->TouchRadius);
            endLU.y = beginPointer.y - Touchsize.y * (touch_information->TouchRadius);
            endRD.x = beginPointer.x + Touchsize.x * (touch_information->TouchRadius);
            endRD.y = beginPointer.y + Touchsize.y * (touch_information->TouchRadius);
            maxMove.x = (Touchsize.x * (touch_information->TouchRadius) / (touch_information->floatswitch[0] * 0.2));
            maxMove.y = (Touchsize.y * (touch_information->TouchRadius) / (touch_information->floatswitch[0] * 0.2));
            ThreadSpeed = 2000 / touch_information->floatswitch[0];
            botio = false;
        }

        // 设置触摸点
        void setTouch()
        {
            if (resolution_information->Orientation == 1)
            {
                beginPointer.x = Touchsize.x * touch_information->TouchPoints.x;
                beginPointer.y = Touchsize.y * touch_information->TouchPoints.y;
                endLU.x = beginPointer.x - Touchsize.x * (touch_information->TouchRadius);
                endLU.y = beginPointer.y - Touchsize.y * (touch_information->TouchRadius);
                endRD.x = beginPointer.x + Touchsize.x * (touch_information->TouchRadius);
                endRD.y = beginPointer.y + Touchsize.y * (touch_information->TouchRadius);
                maxMove.x = (Touchsize.x * touch_information->floatswitch[3] / touch_information->floatswitch[0]);
                maxMove.y = (Touchsize.y * touch_information->floatswitch[3] / touch_information->floatswitch[0]);
                ThreadSpeed = 2000 / touch_information->floatswitch[0];
            }
            else if (resolution_information->Orientation == 3)
            {
                beginPointer.x = Touchsize.x * (1 - touch_information->TouchPoints.x);
                beginPointer.y = Touchsize.y * (1 - touch_information->TouchPoints.y);
                endLU.x = beginPointer.x - Touchsize.x * (touch_information->TouchRadius);
                endLU.y = beginPointer.y - Touchsize.y * (touch_information->TouchRadius);
                endRD.x = beginPointer.x + Touchsize.x * (touch_information->TouchRadius);
                endRD.y = beginPointer.y + Touchsize.y * (touch_information->TouchRadius);
                maxMove.x = (Touchsize.x * touch_information->floatswitch[3] / touch_information->floatswitch[0]);
                maxMove.y = (Touchsize.y * touch_information->floatswitch[3] / touch_information->floatswitch[0]);
                ThreadSpeed = 2000 / touch_information->floatswitch[0];
            }
        }

        // 开始瞄准
        bool StartAim()
        {
            if (botio)
            {
                clock_gettime(CLOCK_MONOTONIC, &endtime);
                loadTime = ((1000000000 * endtime.tv_sec) + (endtime.tv_nsec)) - ((1000000000 * strtime.tv_sec) + (strtime.tv_nsec));
                if (loadTime >= 5000000)
                {
                    nowPointer = beginPointer;
                    return true;
                }
                return false;
            }
            else
            {
                nowPointer = beginPointer;
                botio = true;
                botstart = true;
                mspeed.x = 0.2;
                mspeed.y = 0.2;
                return true;
            }
        }

        // 获取最小角度
        float GetMinAngle(float Target, float self, float offset)
        {
            float d1, d2;
            Target += offset;
            d1 = Target - self;
            d2 = 360.0 - fabs(d1);
            if (d1 > 0)
            {
                d2 *= -1.0;
            }
            return fabs(d1) < fabs(d2) ? d1 : d2;
        }

        // 设置触摸比例
        void setTouchProportion(VecTor2 *TouchProportion_)
        {
            TouchProportion = TouchProportion_;
        }

        // 瞄准旋转算法
        bool AimRotArithmetic(VecTor2 TouchMouses, VecTor2 TouchAimRot)
        {
            if (isnormal(TouchAimRot.x) && isnormal(TouchAimRot.y))
            {
                if (resolution_information->Orientation == 1)
                {
                    AimRot.x = GetMinAngle(TouchAimRot.x, TouchMouses.x, 0.05);
                    AimRot.y = GetMinAngle(TouchAimRot.y, TouchMouses.y, 0.03);
                }
                else if (resolution_information->Orientation == 3)
                {
                    AimRot.x = -GetMinAngle(TouchAimRot.x, TouchMouses.x, 0.05);
                    AimRot.y = -GetMinAngle(TouchAimRot.y, TouchMouses.y, 0.03);
                }
                if (fabs(AimRot.x) > (0.25 / touch_information->Scal))
                {
                    mspeed.x += 0.1;
                    if (mspeed.x > (touch_information->floatswitch[2] * ThreadSpeed) * touch_information->Scal)
                    {
                        mspeed.x = (touch_information->floatswitch[2] * ThreadSpeed);
                    }
                }
                else
                {
                    mspeed.x -= 0.1;
                    if (mspeed.x < 0.2)
                    {
                        mspeed.x = 0.2;
                    }
                }
                if (fabs(AimRot.y) > (0.25 / touch_information->Scal))
                {
                    mspeed.y += 0.1;
                    if (mspeed.y > (touch_information->floatswitch[1] * ThreadSpeed) * touch_information->Scal)
                    {
                        mspeed.y = (touch_information->floatswitch[1] * ThreadSpeed);
                    }
                }
                else
                {
                    mspeed.y -= 0.1;
                    if (mspeed.y < 0.2)
                    {
                        mspeed.y = 0.2;
                    }
                }
                if (fabs(AimRot.x) > maxMove.x)
                {
                    AimMove.x = AimRot.x > 0 ? maxMove.x : -(maxMove.x);
                    nowPointer.x += AimMove.x;
                }
                else
                {
                    AimMove.x = (AimRot.x * mspeed.x) * TouchProportion->x;
                    nowPointer.x += AimMove.x;
                }
                if (fabs(AimRot.y) > maxMove.y)
                {
                    AimMove.y = AimRot.y > 0 ? maxMove.y : -(maxMove.y);
                    nowPointer.y += AimMove.y;
                }
                else
                {
                    AimMove.y = (AimRot.y * mspeed.y) * TouchProportion->y;
                    nowPointer.y += AimMove.y;
                }
                if (touch_information->touchLockMode == 1) // 硬锁模式
                {
                    if (nowPointer.x < endLU.x || nowPointer.y < endLU.y || nowPointer.x > endRD.x || nowPointer.y > endRD.y)
                    {
                        clock_gettime(CLOCK_MONOTONIC, &strtime);
                        return false;
                    }
                }
                return true;
            }
            else
            {
                if (nowPointer.x < endLU.x || nowPointer.y < endLU.y || nowPointer.x > endRD.x || nowPointer.y > endRD.y)
                {
                    clock_gettime(CLOCK_MONOTONIC, &strtime);
                    return false;
                }
                return true;
            }
        }

        // 结束机器人触摸
        void botEnd()
        {
            botio = false;
        }
    };

    // 触摸输入结构体
    struct touchInput
    {
        int pos;                                                  // 位置
        int lastid;                                               // 上一个ID
        Touch bottouch;                                           // 机器人触摸
        size_t outsize;                                           // 输出大小
        Touch touch[16];                                          // 触摸数组
        Touch *outTouch[16];                                      // 输出触摸指针数组
        VecTor2 *TouchProportion;                                 // 触摸比例
        Touch *touchNow = nullptr;                                // 当前触摸
        Touch *touchLast = nullptr;                               // 上一个触摸
        struct input_event oneevent;                              // 单个事件
        TOUCH_INFORMATION *touch_information = nullptr;           // 触摸信息
        RESOLUTION_INFORMATION *resolution_information = nullptr; // 分辨率信息

        // 设置触摸比例
        void setTouchProportion(VecTor2 *TouchProportion_)
        {
            TouchProportion = TouchProportion_;
        }

        // 构造函数，初始化触摸信息和分辨率信息
        touchInput(TOUCH_INFORMATION *_touch_information, RESOLUTION_INFORMATION *_resolution_information)
        {
            if (_touch_information != nullptr && _resolution_information != nullptr)
            {
                touch_information = _touch_information;
                resolution_information = _resolution_information;
            }
            bzero(touch, sizeof(touch));
            if (touchNow == nullptr)
            {
                touchNow = &touch[0];
            }
            pos = 0;
            for (int l = 0; l < 16; ++l)
            {
                touch[l].SLOT = l;
            }
            SLOTio = false;
            outsize = 0;
        }

        // 打开新的触摸
        Touch *opennew(int s)
        {
            int in = 0;
            for (in = 0; in < 16; ++in)
            {
                if (!touch[in].io)
                {
                    break;
                }
            }
            touch[in].io = true;
            touch[in].Action = true;
            touch[in].run = true;
            touchLast = &touch[in];
            touchLast->TRACKING_ID = s;
            ++pos;
            return touchLast;
        }

        bool SLOTio = false; // 槽位输入输出标志
        // 获取当前槽位
        Touch *SLOT_getnow(int idi, bool bot)
        {
            if (bot)
            {
                if (!bottouch.io)
                {
                    bottouch.Action = true;
                    bottouch.run = true;
                }
                bottouch.isbot = bot;
                bottouch.SLOT = idi;
                bottouch.io = true;
                return touchNow;
            }
            else
            {
                SLOTio = true;
                if (!touch[idi].io)
                {
                    ++pos;
                    touch[idi].Action = true;
                    touch[idi].run = true;
                }
                touch[idi].io = true;
                touch[idi].SLOT = idi;
                touchLast = &touch[idi];
                return touchLast;
            }
        }

        // 关闭当前触摸
        void closenow(Touch *last)
        {
            if (pos > 0)
            {
                --pos;
            }
            last->io = false;
            last->TRACKING_ID = -1;
        }

        // 查找当前追踪ID
        Touch *TRACKING_ID_findnow(int ss, bool bot)
        {
            if (bot)
            {
                if (ss == -1)
                {
                    bottouch.io = false;
                }
                bottouch.TRACKING_ID = ss;
                return touchNow;
            }
            else
            {
                bool a = false;
                if (ss == -1)
                {
                    closenow(touchNow);
                    SLOTio = false;
                    return touchNow;
                }
                if (SLOTio)
                {
                    touchLast->TRACKING_ID = ss;
                    SLOTio = false;
                    return touchLast;
                }
                int in = 0;
                for (in = 0; in < 16; ++in)
                {
                    if (touch[in].TRACKING_ID == ss)
                    {
                        a = true;
                        break;
                    }
                }
                if (a)
                {
                    touchLast = &touch[in];
                    SLOTio = false;
                    return touchLast;
                }
                else
                {
                    SLOTio = false;
                    return opennew(ss);
                }
            }
        }

        // 设置X坐标
        void ABS_SetX(int x, bool bot)
        {
            if (bot)
            {
                bottouch.POSITION_X = x;
                bottouch.runing = true;
            }
            else
            {
                touchNow->POSITION_X = x;
                touchNow->runing = true;
            }
        }

        // 设置Y坐标
        void ABS_SetY(int y, bool bot)
        {
            if (bot)
            {
                bottouch.POSITION_Y = y;
                bottouch.runing = true;
            }
            else
            {
                touchNow->POSITION_Y = y;
                touchNow->runing = true;
            }
        }

        // 旋转点函数，根据屏幕方向调整坐标
        VecTor2 RotatePoint(float PointX, float PointY, VecTor2 Display)
        {
            VecTor2 Rotate = {0, 0};
            if (resolution_information->Orientation == 3)
            {
                Rotate.x = Display.y - PointY;
                Rotate.y = PointX;
            }
            else if (resolution_information->Orientation == 2)
            {
                Rotate.x = Display.x - PointX;
                Rotate.y = Display.y - PointY;
            }
            else if (resolution_information->Orientation == 1)
            {
                Rotate.x = PointY;
                Rotate.y = Display.x - PointX;
            }
            else if (resolution_information->Orientation == 0)
            {
                Rotate.x = PointX;
                Rotate.y = PointY;
            }
            return Rotate;
        }

        int touchnewX = 0; // 新的X坐标
        int touchnewY = 0; // 新的Y坐标
        // 添加事件
        int add_event(input_event &oneevent, bool isbot)
        {
            int SYN = 0;
            switch (oneevent.code)
            {
            case ABS_MT_POSITION_X:
                touchnewX = oneevent.value;
                ABS_SetX(oneevent.value, isbot);
                break;
            case ABS_MT_POSITION_Y:
                touchnewY = oneevent.value;
                ABS_SetY(oneevent.value, isbot);
                break;
            case ABS_MT_TRACKING_ID:
                touchNow = TRACKING_ID_findnow(oneevent.value, isbot);
                touchNow->isbot = isbot;
                if ((int)oneevent.value >= 1)
                {
                    ImGui::GetIO().MouseDown[0] = true;
                }
                else
                {
                    ImGui::GetIO().MouseDown[0] = false;
                }
                break;
            case ABS_MT_SLOT:
                touchNow = SLOT_getnow(oneevent.value, isbot);
                touchNow->isbot = isbot;
                break;
            case SYN_REPORT:
                SYN = 1;
                break;
            }
            VecTor2 Point = RotatePoint(touchnewX, touchnewY, touch_information->TouchScreenSize);
            ImGui::GetIO().MousePos = ImVec2((Point.x * resolution_information->FixedScreenWidth) / touch_information->TouchScreenSize.x, (Point.y * resolution_information->FixedScreenHeiht) / touch_information->TouchScreenSize.y);
            if (SYN)
            {
                return 1;
            }
            return 0;
        }

        int upcon;      // 更新计数
        int tmpsize;    // 临时大小
        Touch tmp[9];   // 临时触摸数组
        int thindx[9];  // 临时索引数组
        bool jk = true; // 标志
        int tmpcon = 0; // 临时计数
        Timer movetiem; // 移动时间计时器
        // 获取正确的触摸
        void GetRightTouch()
        {
            if (jk)
            {
                tmpcon = 0;
                for (int l = 0; l < outsize; ++l)
                {
                    if (outTouch[l]->io)
                    {
                        if (resolution_information->Orientation == 1)
                        {
                            if (!outTouch[l]->isbot && outTouch[l]->POSITION_Y / TouchProportion->y > (resolution_information->ScreenWidth / 2))
                            {
                                tmp[tmpcon] = *outTouch[l];
                                thindx[tmpcon] = l;
                                touch_information->TouchAimAtControl = false;
                                jk = false;
                                ++tmpcon;
                                movetiem.looptimesta();
                            }
                        }
                        else if (resolution_information->Orientation == 3)
                        {
                            if (!outTouch[l]->isbot && outTouch[l]->POSITION_Y / TouchProportion->y < (resolution_information->ScreenWidth / 2))
                            {
                                tmp[tmpcon] = *outTouch[l];
                                thindx[tmpcon] = l;
                                touch_information->TouchAimAtControl = false;
                                jk = false;
                                ++tmpcon;
                                movetiem.looptimesta();
                            }
                        }
                    }
                }
                tmpsize = tmpcon;
            }
            else
            {
                upcon = 0;
                for (int l = 0; l < tmpsize; ++l)
                {
                    if (outTouch[l]->TRACKING_ID == -1)
                    {
                        ++upcon;
                    }
                    else
                    {
                        if (movetiem.getlooptime() >= 150000000)
                        {
                            if (abs(tmp[l].POSITION_Y - outTouch[thindx[l]]->POSITION_Y) > 100 || abs(tmp[l].POSITION_X - outTouch[thindx[l]]->POSITION_X) > 100)
                            {
                                touch_information->TouchAimAtControl = true;
                                tmp[l].POSITION_Y = outTouch[thindx[l]]->POSITION_Y;
                                tmp[l].POSITION_X = outTouch[thindx[l]]->POSITION_X;
                            }
                            else
                            {
                                touch_information->TouchAimAtControl = false;
                                tmp[l].POSITION_Y = outTouch[thindx[l]]->POSITION_Y;
                                tmp[l].POSITION_X = outTouch[thindx[l]]->POSITION_X;
                                jk = true;
                            }
                            movetiem.looptimesta();
                        }
                    }
                }
                if (upcon == tmpsize)
                {
                    jk = true;
                    touch_information->TouchAimAtControl = false;
                }
            }
        }

        // 获取输出事件
        void getOutEvent()
        {
            int un = 0;
            for (int l = 0; l < 10; ++l)
            {
                if (touch[l].run)
                {
                    outTouch[un] = &touch[l];
                    ++un;
                }
            }
            if (bottouch.run)
            {
                outTouch[un] = &bottouch;
                ++un;
            }
            outsize = un;
        }

        // 读取事件
        void readEvent()
        {
            for (;;)
            {
                if (read(touch_information->TouchDeviceFile, &oneevent, sizeof(oneevent)) > 0)
                {
                    if (add_event(oneevent, false))
                    {
                        break;
                    }
                }
                else
                {
                    break;
                }
            }
        }
    };

    // 触摸输出结构体
    struct touchUnput
    {
        float proportionX;                                                                    // X比例
        float proportionY;                                                                    // Y比例
        float POSITION_X_Max;                                                                 // 最大X坐标
        float POSITION_Y_Max;                                                                 // 最大Y坐标
        bool *mainio = nullptr;                                                               // 主输入输出标志
        int *write_fb = nullptr;                                                              // 写入帧缓冲
        touchInput *intouchdata = nullptr;                                                    // 输入数据
        struct events *eventdata = nullptr;                                                   // 事件数据
        struct input_event ID, SLOT, SLOTAI;                                                  // 输入事件
        TOUCH_INFORMATION *touch_information = nullptr;                                       // 触摸信息
        RESOLUTION_INFORMATION *resolution_information = nullptr;                             // 分辨率信息
        struct input_event PRESSURE, TOUCH_MAJOR, WIDTH_MAJOR, FINGER, inx, iny, SYN, BTOUCH; // 输入事件

        // 设置主输入输出标志
        void setMainio(bool *io)
        {
            mainio = io;
        }

        // 设置事件数据
        void setEventData(struct events *eventdata_)
        {
            eventdata = eventdata_;
        }

        // 设置输入数据
        void setInTouchData(touchInput *intouchdata_)
        {
            intouchdata = intouchdata_;
        }

        // 设置写入帧缓冲
        void setWritefb(int *fd)
        {
            write_fb = fd;
        }

        // 构造函数，初始化触摸信息和分辨率信息
        touchUnput(TOUCH_INFORMATION *_touch_information, RESOLUTION_INFORMATION *_resolution_information)
        {
            if (_touch_information != nullptr && _resolution_information != nullptr)
            {
                touch_information = _touch_information;
                resolution_information = _resolution_information;
            }
        }

        // 触摸槽位
        void Touch_BTOUCH_SLOT(int io)
        {
            gettimeofday(&BTOUCH.time, 0);
            BTOUCH.value = io;
            write(*write_fb, &BTOUCH, sizeof(struct input_event));
        }

        // 触摸动作槽位
        void Touch_Action_SLOT(int x, int y, int slot, int trackingid)
        {
            gettimeofday(&SLOTAI.time, 0);
            SLOTAI.value = slot;
            write(*write_fb, &SLOTAI, sizeof(struct input_event));
            gettimeofday(&ID.time, 0);
            ID.value = trackingid;
            write(*write_fb, &ID, sizeof(struct input_event));
            gettimeofday(&WIDTH_MAJOR.time, 0);
            write(*write_fb, &WIDTH_MAJOR, sizeof(struct input_event));
            gettimeofday(&TOUCH_MAJOR.time, 0);
            write(*write_fb, &TOUCH_MAJOR, sizeof(struct input_event));
            gettimeofday(&PRESSURE.time, 0);
            write(*write_fb, &PRESSURE, sizeof(struct input_event));
            if (x != -1)
            {
                gettimeofday(&inx.time, 0);
                inx.value = x;
                write(*write_fb, &inx, sizeof(struct input_event));
            }
            if (y != -1)
            {
                gettimeofday(&iny.time, 0);
                iny.value = y;
                write(*write_fb, &iny, sizeof(struct input_event));
            }
        }

        // 触摸移动槽位
        void Touch_Move_SLOT(int x, int y, int slot)
        {
            gettimeofday(&SLOTAI.time, 0);
            SLOTAI.value = slot;
            write(*write_fb, &SLOTAI, sizeof(struct input_event));
            if (x != -1)
            {
                gettimeofday(&inx.time, 0);
                inx.value = x;
                write(*write_fb, &inx, sizeof(struct input_event));
            }
            if (y != -1)
            {
                gettimeofday(&iny.time, 0);
                iny.value = y;
                write(*write_fb, &iny, sizeof(struct input_event));
            }
        }

        // 触摸同步槽位
        void Touch_SYN_SLOT()
        {
            gettimeofday(&SYN.time, 0);
            write(*write_fb, &SYN, sizeof(struct input_event));
        }

        // 关闭触摸槽位
        void Touch_CLOSE_SLOT(int slot)
        {
            gettimeofday(&SLOTAI.time, 0);
            SLOTAI.value = slot;
            write(*write_fb, &SLOTAI, sizeof(struct input_event));
            gettimeofday(&ID.time, 0);
            ID.value = -1;
            write(*write_fb, &ID, sizeof(struct input_event));
        }

        // 生成随机字符串
        char *randomString(int length)
        {
            int flag, i;
            srand((unsigned)time(NULL));
            char *tmpString = (char *)malloc(length * sizeof(char));
            for (i = 0; i < length - 1; i++)
            {
                flag = rand() % 3;
                switch (flag)
                {
                case 0:
                    tmpString[i] = 'A' + rand() % 26;
                    break;
                case 1:
                    tmpString[i] = 'a' + rand() % 26;
                    break;
                case 2:
                    tmpString[i] = '0' + rand() % 10;
                    break;
                default:
                    tmpString[i] = 'x';
                    break;
                }
            }
            tmpString[length - 1] = '\0';
            return tmpString;
        }

        // 创建触摸屏
        int createTouchScreen(int *fd_, struct events *eventdata)
        {
            static int uinp_fd;
            struct uinput_user_dev uinp;
            uinp_fd = open("/dev/uinput", O_RDWR);
            if (uinp_fd == 0)
            {
                return 0;
            }
            memset(&uinp, 0, sizeof(uinp));
            strncpy(uinp.name, randomString(rand() % 20), UINPUT_MAX_NAME_SIZE);
            uinp.id.bustype = ID_BUS;
            uinp.id.version = rand() % 10;
            uinp.id.vendor = rand() % 20;
            uinp.id.product = rand() % 30;
            for (int ev = 0; ev < EV_MAX; ++ev)
            {
                if (eventdata[ev].io)
                {
                    ioctl(uinp_fd, UI_SET_EVBIT, eventdata[ev].ID);
                    if (eventdata[ev].ID == 0)
                    {
                        continue;
                    }
                    if (eventdata[ev].infoio)
                    {
                        for (int i = 0; i < ABS_MAX; ++i)
                        {
                            if (eventdata[ev].eventmsg[i].io)
                            {
                                ioctl(uinp_fd, UI_SET_ABSBIT, eventdata[ev].eventmsg[i].ID);
                                if (ABS_MT_POSITION_X == eventdata[ev].eventmsg[i].ID)
                                {
                                    POSITION_X_Max = eventdata[ev].eventmsg[i].data.maximum;
                                    proportionX = POSITION_X_Max / (resolution_information->ScreenHeiht < resolution_information->ScreenWidth ? resolution_information->ScreenHeiht : resolution_information->ScreenWidth);
                                }
                                if (ABS_MT_POSITION_Y == eventdata[ev].eventmsg[i].ID)
                                {
                                    POSITION_Y_Max = eventdata[ev].eventmsg[i].data.maximum;
                                    proportionY = POSITION_Y_Max / (resolution_information->ScreenHeiht > resolution_information->ScreenWidth ? resolution_information->ScreenHeiht : resolution_information->ScreenWidth);
                                }
                                uinp.absmin[eventdata[ev].eventmsg[i].ID] = eventdata[ev].eventmsg[i].data.minimum;
                                uinp.absmax[eventdata[ev].eventmsg[i].ID] = eventdata[ev].eventmsg[i].data.maximum;
                                uinp.absfuzz[eventdata[ev].eventmsg[i].ID] = eventdata[ev].eventmsg[i].data.fuzz;
                                uinp.absflat[eventdata[ev].eventmsg[i].ID] = eventdata[ev].eventmsg[i].data.flat;
                            }
                        }
                    }
                    else
                    {
                        for (int i = 0; i < KEY_MAX; ++i)
                        {
                            if (eventdata[ev].eventmsg[i].io)
                            {
                                ioctl(uinp_fd, UI_SET_KEYBIT, eventdata[ev].eventmsg[i].ID);
                            }
                        }
                    }
                }
            }
            ioctl(uinp_fd, UI_SET_PROPBIT, INPUT_PROP_DIRECT);
            write(uinp_fd, &uinp, sizeof(uinp));
            ioctl(uinp_fd, UI_DEV_CREATE);
            *fd_ = uinp_fd;
            while (true)
            {
                if (!mainio)
                {
                    break;
                }
                usleep(1000000);
            }
            return 1;
        }

        // 初始化输入
        int unputinit()
        {
            PRESSURE.code = ABS_MT_PRESSURE;
            PRESSURE.type = EV_ABS;
            PRESSURE.value = 20;
            TOUCH_MAJOR.type = EV_ABS;
            TOUCH_MAJOR.code = ABS_MT_TOUCH_MAJOR;
            TOUCH_MAJOR.value = 15;
            WIDTH_MAJOR.type = EV_ABS;
            WIDTH_MAJOR.code = ABS_MT_WIDTH_MAJOR;
            WIDTH_MAJOR.value = 10;
            ID.type = EV_ABS;
            ID.code = ABS_MT_TRACKING_ID;
            SLOT.type = EV_ABS;
            SLOT.code = ABS_MT_SLOT;
            SLOTAI.type = EV_ABS;
            SLOTAI.code = ABS_MT_SLOT;
            FINGER.type = EV_KEY;
            FINGER.code = BTN_TOOL_FINGER;
            inx.type = EV_ABS;
            inx.code = ABS_MT_POSITION_X;
            iny.type = EV_ABS;
            iny.code = ABS_MT_POSITION_Y;
            SYN.type = EV_SYN;
            SYN.code = SYN_REPORT;
            SYN.value = 0;
            BTOUCH.type = EV_KEY;
            BTOUCH.code = BTN_TOUCH;
            thread Thread_UnPut([this]
                                { createTouchScreen(write_fb, eventdata); });
            Thread_UnPut.detach();
            int counts = 0;
            while (true)
            {
                counts++;
                if (*write_fb > 0)
                {
                    return 1;
                }
                if (counts > 10 && *write_fb < 0)
                {
                    return 0;
                }
                usleep(100000);
            }
        }

        bool start = false; // 开始标志
        // 写入事件
        void writeEvent()
        {
            if (intouchdata->outsize)
            {
                if (!start)
                {
                    Touch_BTOUCH_SLOT(1);
                    start = true;
                }
                for (int i = 0; i < intouchdata->outsize; ++i)
                {
                    if (intouchdata->outTouch[i]->TRACKING_ID > 0)
                    {
                        if (intouchdata->outTouch[i]->Action)
                        {
                            Touch_Action_SLOT(intouchdata->outTouch[i]->POSITION_X, intouchdata->outTouch[i]->POSITION_Y, intouchdata->outTouch[i]->SLOT, intouchdata->outTouch[i]->TRACKING_ID);
                            intouchdata->outTouch[i]->Action = false;
                            intouchdata->outTouch[i]->runing = false;
                        }
                        else
                        {
                            if (intouchdata->outTouch[i]->runing)
                            {
                                Touch_Move_SLOT(intouchdata->outTouch[i]->POSITION_X, intouchdata->outTouch[i]->POSITION_Y, intouchdata->outTouch[i]->SLOT);
                                intouchdata->outTouch[i]->runing = false;
                            }
                        }
                    }
                    else
                    {
                        Touch_CLOSE_SLOT(intouchdata->outTouch[i]->SLOT);
                        Touch_SYN_SLOT();
                        intouchdata->outTouch[i]->run = false;
                    }
                }
                Touch_SYN_SLOT();
            }
            else
            {
                if (start)
                {
                    for (int i = 0; i < intouchdata->outsize; ++i)
                    {
                        if (intouchdata->outTouch[i]->TRACKING_ID > 0)
                        {
                            if (intouchdata->outTouch[i]->Action)
                            {
                                Touch_Action_SLOT(intouchdata->outTouch[i]->POSITION_X, intouchdata->outTouch[i]->POSITION_Y, intouchdata->outTouch[i]->SLOT, intouchdata->outTouch[i]->TRACKING_ID);
                                intouchdata->outTouch[i]->Action = false;
                            }
                            else
                            {
                                if (intouchdata->outTouch[i]->runing)
                                {
                                    Touch_Move_SLOT(intouchdata->outTouch[i]->POSITION_X, intouchdata->outTouch[i]->POSITION_Y, intouchdata->outTouch[i]->SLOT);
                                    intouchdata->outTouch[i]->runing = false;
                                }
                            }
                        }
                        else
                        {
                            Touch_CLOSE_SLOT(intouchdata->outTouch[i]->SLOT);
                            Touch_SYN_SLOT();
                            intouchdata->outTouch[i]->run = false;
                        }
                    }
                    Touch_BTOUCH_SLOT(0);
                    Touch_SYN_SLOT();
                    start = false;
                }
            }
        }
    };

public:
    // 构造函数
    touch() {
        kma_driver = nullptr;
    }

    // 析构函数
    ~touch() {
        if (kma_driver) {
            delete kma_driver;
            kma_driver = nullptr;
        }
    }

    // 打印可能的事件
    static int print_possible_events(int fd, struct events *outdata)
    {
        int ress = 0;
        int res, res2;
        const char *label;
        uint8_t *bits = NULL;
        ssize_t bits_size = 0;
        memset(outdata, 0, sizeof(events) * EV_MAX + 1);
        for (int i = 0; i <= EV_MAX; i++)
        {
            int count = 0;
            while (true)
            {
                res = ioctl(fd, EVIOCGBIT(i, bits_size), bits);
                if (res < bits_size)
                {
                    break;
                }
                bits_size = res + 16;
                bits = (uint8_t *)realloc(bits, bits_size * 2);
                if (bits == NULL)
                {
                    return ress;
                }
            }
            res2 = 0;
            switch (i)
            {
            case EV_SYN:
                label = "SYN";
                break;
            case EV_KEY:
                res2 = ioctl(fd, EVIOCGKEY(res), bits + bits_size);
                label = "KEY";
                break;
            case EV_REL:
                label = "REL";
                break;
            case EV_ABS:
                label = "ABS";
                break;
            case EV_MSC:
                label = "MSC";
                break;
            case EV_LED:
                res2 = ioctl(fd, EVIOCGLED(res), bits + bits_size);
                label = "LED";
                break;
            case EV_SND:
                res2 = ioctl(fd, EVIOCGSND(res), bits + bits_size);
                label = "SND";
                break;
            case EV_SW:
                res2 = ioctl(fd, EVIOCGSW(bits_size), bits + bits_size);
                label = "SW ";
                break;
            case EV_REP:
                label = "REP";
                break;
            case EV_FF:
                label = "FF ";
                break;
            case EV_PWR:
                label = "PWR";
                break;
            default:
                res2 = 0;
                label = "???";
            }
            for (int j = 0; j < res; j++)
            {
                for (int k = 0; k < 8; k++)
                {
                    if (bits[j] & 1 << k)
                    {
                        char down;
                        if (j < res2 && (bits[j + bits_size] & 1 << k))
                        {
                            down = '*';
                        }
                        else
                        {
                            down = ' ';
                        }
                        if (count == 0)
                        {
                            outdata[i].ID = i;
                            outdata[i].io = true;
                            outdata[i].eventmsg[j * 8 + k].ID = j * 8 + k;
                            outdata[i].eventmsg[j * 8 + k].io = true;
                        }
                        else if ((count & 0x7) == 0 || i == EV_ABS)
                        {
                            outdata[i].ID = i;
                        }
                        outdata[i].io = true;
                        outdata[i].eventmsg[j * 8 + k].ID = j * 8 + k;
                        outdata[i].eventmsg[j * 8 + k].io = true;
                        if (i == EV_ABS)
                        {
                            outdata[i].ID = i;
                            outdata[i].io = true;
                            outdata[i].eventmsg[j * 8 + k].ID = j * 8 + k;
                            outdata[i].eventmsg[j * 8 + k].io = true;
                            struct input_absinfo abs;
                            if (ioctl(fd, EVIOCGABS(j * 8 + k), &abs) == 0)
                            {
                                ++ress;
                                outdata[i].infoio = true;
                                outdata[i].eventmsg[j * 8 + k].data = abs;
                            }
                        }
                        count++;
                    }
                }
            }
        }
        if (bits != nullptr)
        {
            free(bits);
        }
        return ress;
    }

    // 初始化输入
    int initInput()
    {
        if (touch_information->TouchDeviceFile < 0)
        {
            return touch_information->TouchDeviceFile;
        }
        botID.type = EV_ABS;
        botID.code = ABS_MT_TRACKING_ID;
        botSLOT.type = EV_ABS;
        botSLOT.code = ABS_MT_SLOT;
        botX.type = EV_ABS;
        botX.code = ABS_MT_POSITION_X;
        botY.type = EV_ABS;
        botY.code = ABS_MT_POSITION_Y;
        botSYN.type = EV_SYN;
        botSYN.code = SYN_REPORT;
        botSYN.value = 0;
        return touch_information->TouchDeviceFile;
    }

    // 设置瞄准输入输出标志
    void setAimIo(bool Aimio_)
    {
        Aimio = Aimio_;
    }

    struct input_event botSYN, botSLOT, botID, botX, botY; // 机器人输入事件
    // 机器人触摸动作槽位
    void Bot_Touch_Action_SLOT(touchInput &input, int SLOT, int ID, VecTor2 &TouchPointer)
    {
        // 使用kma_driver进行触摸按下操作
        if (kma_driver) {
            kma_driver->uinput_down((int)TouchPointer.x, (int)TouchPointer.y);
        } else {
            // 保留原有的实现作为备用（已注释）
            /*
            botSLOT.value = SLOT;
            input.add_event(botSLOT, true);
            botID.value = ID;
            input.add_event(botID, true);
            botX.value = TouchPointer.x;
            input.add_event(botX, true);
            botY.value = TouchPointer.y;
            input.add_event(botY, true);
            input.add_event(botSYN, true);
            */
        }
    }

    // 机器人触摸移动槽位
    void Bot_Touch_Move_SLOT(touchInput &input, int SLOT, VecTor2 &TouchPointer)
    {
        // 使用kma_driver进行触摸移动操作
        if (kma_driver) {
            kma_driver->uinput_move((int)TouchPointer.x, (int)TouchPointer.y);
            // 添加延时机制，防止因速度过快导致的跳跃问题
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        } else {
            // 保留原有的实现作为备用（已注释）
            /*
            botSLOT.value = SLOT;
            input.add_event(botSLOT, true);
            botX.value = TouchPointer.x;
            input.add_event(botX, true);
            botY.value = TouchPointer.y;
            input.add_event(botY, true);
            input.add_event(botSYN, true);
            */
        }
    }

    // 关闭机器人触摸槽位
    void Bot_Touch_CLOSE_SLOT(touchInput &input, int SLOT)
    {
        // 使用kma_driver进行触摸抬起操作
        if (kma_driver) {
            kma_driver->uinput_up();
        } else {
            // 保留原有的实现作为备用（已注释）
            /*
            botSLOT.value = SLOT;
            input.add_event(botSLOT, true);
            botID.value = -1;
            input.add_event(botID, true);
            input.add_event(botSYN, true);
            */
        }
    }

    // 获取触摸比例
    void GetTouchProportion(float touchmax_X, float touchmax_Y)
    {
        FullScreenX = resolution_information->FixedScreenWidth;
        FullScreenY = resolution_information->FixedScreenHeiht;
        TouchProportion.x = touchmax_X / FullScreenY;
        TouchProportion.y = touchmax_Y / FullScreenX;
    }

    // 设置开火输入输出标志
    void setFireIo(bool io)
    {
        IsFire = io;
    }

    // 获取触摸
    void GetTouch(TOUCH_INFORMATION *_touch_information, RESOLUTION_INFORMATION *_resolution_information, bool isreadtouch)
    {
        if (_touch_information != nullptr && _resolution_information != nullptr)
        {
            touch_information = _touch_information;
            resolution_information = _resolution_information;

            // 初始化kma_driver用于自瞄触摸
            if (!kma_driver) {
                kma_driver = new Driver();
                if (kma_driver) {
                    kma_driver->uinput_init(resolution_information->ScreenWidth, resolution_information->ScreenHeiht);
                }
            }
        }
        int touchnewX = 0;
        int touchnewY = 0;
        if (isreadtouch)
        {
            while (touch_information->TouchDeviceFile)
            {
                struct input_event oneevent;
                if (read(touch_information->TouchDeviceFile, &oneevent, sizeof(oneevent)))
                {
                    if (oneevent.code == ABS_MT_POSITION_X)
                    {
                        touchnewX = oneevent.value;
                    }
                    if (oneevent.code == ABS_MT_POSITION_Y)
                    {
                        touchnewY = oneevent.value;
                    }
                    if (oneevent.code == ABS_MT_TRACKING_ID)
                    {
                        if ((int)oneevent.value >= 1)
                        {
                            ImGui::GetIO().MouseDown[0] = true;
                        }
                        else
                        {
                            ImGui::GetIO().MouseDown[0] = false;
                        }
                    }
                    VecTor2 Point = RotatePoint(touchnewX, touchnewY, touch_information->TouchScreenSize);
                    ImGui::GetIO().MousePos = ImVec2((Point.x * resolution_information->FixedScreenWidth) / touch_information->TouchScreenSize.x, (Point.y * resolution_information->FixedScreenHeiht) / touch_information->TouchScreenSize.y);
                }
                this_thread::sleep_for(1ms);
            }
        }
        else if (!isreadtouch)
        {
            touchInput InPut(_touch_information, _resolution_information);
            touchUnput UnPut(_touch_information, _resolution_information);
            Aimio = false;
            if (initInput() < 0)
            {
                return;
            }
            print_possible_events(touch_information->TouchDeviceFile, eventdata);
            mainio = true;
            UnPut.setMainio(&mainio);
            UnPut.setWritefb(&write_fb);
            UnPut.setEventData(eventdata);
            UnPut.setInTouchData(&InPut);
            if (!UnPut.unputinit())
            {
                return;
            }
            GetTouchProportion(UnPut.POSITION_X_Max, UnPut.POSITION_Y_Max);
            InPut.setTouchProportion(&TouchProportion);
            BotTouch botTouch(_touch_information, _resolution_information, VecTor2(UnPut.POSITION_X_Max, UnPut.POSITION_Y_Max));
            botTouch.setTouchProportion(&TouchProportion);
            int ioctl_result = ioctl(touch_information->TouchDeviceFile, EVIOCGRAB, 1);
            if (ioctl_result == -1)
            {
                return;
            }

            MainAimiSleep.SetFps(touch_information->floatswitch[0]);
            MainAimiSleep.AotuFPS_init();

            for (;;)
            {
                InPut.readEvent();
                InPut.getOutEvent();
                if (!IsFire)
                {
                    InPut.GetRightTouch();
                }
                else
                {
                    touch_information->TouchAimAtControl = false;
                }
                if (Aimio)
                {
                    if (touch_information->TouchOrientationControl)
                    {
                        GetTouchProportion(UnPut.POSITION_X_Max, UnPut.POSITION_Y_Max);
                        botTouch.setTouch();
                        touch_information->TouchOrientationControl = false;
                    }
                    if (!AimAtIo)
                    {
                        if (botTouch.StartAim())
                        {
                            Bot_Touch_Action_SLOT(InPut, 8, 1000, botTouch.beginPointer);
                            AimAtIo = true;
                        }
                    }
                    else
                    {
                        if (botTouch.AimRotArithmetic(touch_information->MouseCoordinate, touch_information->AimingCoordinates))
                        {
                            Bot_Touch_Move_SLOT(InPut, 8, botTouch.nowPointer);
                        }
                        else
                        {
                            Bot_Touch_CLOSE_SLOT(InPut, 8);
                            AimAtIo = false;
                        }
                    }
                }
                else
                {
                    if (AimAtIo)
                    {
                        botTouch.botEnd();
                        Bot_Touch_CLOSE_SLOT(InPut, 8);
                        AimAtIo = false;
                    }
                }
                InPut.getOutEvent();
                UnPut.writeEvent();
                if (!mainio)
                {
                    return;
                }

                MainAimiSleep.AotuFPS();
                MainAimiSleep.SetFps(touch_information->floatswitch[0]);
            }
        }
    }
};

#endif
