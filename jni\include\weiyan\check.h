#ifndef CHECK_H
#define CHECK_H

#include <iostream>
#include <string>
#include <fstream>
#include <random>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <thread>
#include <atomic>
#include <cstdio>
#include "Util.h"
char buffer[128];
using namespace std;

// 验证配置
const string WEIYAN_DOMAIN = "wy.llua.cn";
static std::atomic<bool> is_running(false);
static std::thread countdown_thread;

// 验证函数声明
bool performVerification();
bool loginWithCard(const string &cardKey);
void startCountdown(long total_seconds);

// 验证函数实现
bool performVerification()
{
    while (true)
    {
        string cardKey = getLastUsedCard();

        if (cardKey.empty())
        {
            std::cout << "卡密: ";
            std::cin >> cardKey;
        }
        else
        {
            std::cout << "卡密: " << cardKey << std::endl;
        }

        if (loginWithCard(cardKey))
        {
            saveCardKey(cardKey);
            return true;
        }
        else
        {
            deleteCardKey();
        }

        std::cout << std::endl;
    }

    return false;
}

bool loginWithCard(const string &cardKey)
{
    string deviceCode = getIMEI();

    // 生成随机数和时间戳
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dist(100000, 999999);

    auto now = std::chrono::system_clock::now();
    auto epoch = now.time_since_epoch();
    auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(epoch).count();

    string timestampStr = std::to_string(timestamp);
    string randomValue = std::to_string(dist(gen));
    string signature = p67bdbe2aae72e510b2b6f0cf2e89dc16("kami=" + cardKey + "&markcode=" + deviceCode + "&t=" + timestampStr + "&had47494bdb05adf4b9b3c90964bba1");

    // 构建请求数据
    string requestData = n7629f6a1cc97bdd28dbea1f56ba860ae(e90b995ae4b3a5e565fc6c4eb63f0cf2e(n7629f6a1cc97bdd28dbea1f56ba860ae(n7629f6a1cc97bdd28dbea1f56ba860ae(iec840521d6468c4f69d26549fb4ee7c5(n7629f6a1cc97bdd28dbea1f56ba860ae(iec840521d6468c4f69d26549fb4ee7c5(e90b995ae4b3a5e565fc6c4eb63f0cf2e(n7629f6a1cc97bdd28dbea1f56ba860ae(iec840521d6468c4f69d26549fb4ee7c5("id=NplpO7gCCEL&kami=" + cardKey + "&markcode=" + deviceCode + "&t=" + timestampStr + "&sign=" + signature + "&value=" + randomValue + "", "w5a1e734a40779d5165db"))), "j229c9a24ea737fa7cafa")), "n7b45d634c013c1d599bad1612ea5e8c153bb")))));

    // 发送请求
    string response = httppost(WEIYAN_DOMAIN, "v2/ddece8d3ddd99a882a86e076167e6ce0", requestData);

    try
    {
        // 解析响应
        json responseJson = json::parse(l182e61f7a646e05ece5173bbbb922b58(dd142d136d223cc8a9cb6dc4f77831202(dd142d136d223cc8a9cb6dc4f77831202(iec840521d6468c4f69d26549fb4ee7c5(j919caee9f02a5ec7417bb14d1f861eab(response), "u2dc14744277ad5c11fb572"), "1wQ9UJOWzRTn48/tEjeClIbFLdKDVh3q2p6AMNiYX+SZy5m7BgxsPGorvafcHku0"), "vjkAPT0KqL+68VNxsDBpSfwzQJUaYIyemtMg2HruW1inZ57G3RdC9EcO/4bhXolF")));

        if (responseJson["sd3c3e7d62f98c81f8f7bebaba5f24c29"] == 15583 && responseJson["ae6495df51f09cef1d7f061a050d01425"]["w1964f81774fbb62aeb5f014ed9d35b6c"] == "5349ee6458dfabfc87aa00b02f11311d")
        {
            long serverTime = responseJson["ca7544ff9e53744334e78f5a4312f5cba"];

            // 检查时间差
            if (serverTime - std::stol(timestampStr) > 30 || serverTime - std::stol(timestampStr) < -30)
            {
                std::cout << "设备时间不准\n"
                          << std::endl;
                return false;
            }

            // 验证签名
            std::string serverTimeStr = std::to_string(serverTime);
            long responseCode = responseJson["sd3c3e7d62f98c81f8f7bebaba5f24c29"];
            std::string responseCodeStr = std::to_string(responseCode);
            long expireTime = responseJson["ae6495df51f09cef1d7f061a050d01425"]["bb37927732900fedb4dceb88e4bac1737"];
            std::string expireTimeStr = std::to_string(expireTime);

            string expectedSign1 = p67bdbe2aae72e510b2b6f0cf2e89dc16(signature + responseCodeStr + "had47494bdb05adf4b9b3c90964bba1" + timestampStr + "f89bdeea8");
            string expectedSign2 = p67bdbe2aae72e510b2b6f0cf2e89dc16(responseCodeStr + expireTimeStr + serverTimeStr + responseCodeStr + serverTimeStr + "a1dd9");
            string expectedSign3 = p67bdbe2aae72e510b2b6f0cf2e89dc16(serverTimeStr + expireTimeStr + expireTimeStr + "f98bb29db");

            if (responseJson["ae6495df51f09cef1d7f061a050d01425"]["g1398385a03cc"] != expectedSign1 ||
                responseJson["ae6495df51f09cef1d7f061a050d01425"]["rabf645e55875c2"] != expectedSign2 ||
                responseJson["ae6495df51f09cef1d7f061a050d01425"]["m7fdbf08f"] != expectedSign3)
            {
                // std::cout << "校验失败\n"
               // << std::endl;
                return false;
            }

            // 显示登录成功信息
            if (responseJson["ae6495df51f09cef1d7f061a050d01425"]["dea2127808ba58aaeae730be9ea402a76"] == "single")
            {
                std::cout << "登录成功，剩余可登录次数：" << responseJson["ae6495df51f09cef1d7f061a050d01425"]["n7ae67f318bfc54848c0f61a537afd73d"] << std::endl;
            }
            else
            {
                long expireTimestamp = responseJson["ae6495df51f09cef1d7f061a050d01425"]["s845434c22759a5a56d57d97dba753a7b"];
                /* std::tm tm = *std::localtime(&expireTimestamp);
                 std::stringstream ss;
                 ss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
                 std::cout << "登录成功，到期时间：" << ss.str() << std::endl;*/
                // 计算剩余时间并启动倒计时
                auto now = std::chrono::system_clock::now();
                auto currentTime = std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count();
                long time_diff = expireTimestamp - currentTime;
                if (time_diff <= 0)
                {
                    std::cout << "卡密已到期" << std::endl;
                    return false;
                }
                else
                {
                    long days = time_diff / (24 * 3600);
                    long hours = (time_diff % (24 * 3600)) / 3600;
                    long minutes = (time_diff % 3600) / 60;
                    long seconds = time_diff % 60;
                    sprintf(buffer, "%ld天 %ld时 %ld分", days, hours, minutes, seconds);
                    std::cout << "剩余时间：" << days << "天 " << hours << "时 " << minutes << "分 " << seconds << "秒" << std::endl;
                    startCountdown(time_diff);
                }
            }

            return true;
        }
        else
        {
            std::string msg = responseJson["ae6495df51f09cef1d7f061a050d01425"];
            std::cout << "验证失败: " << msg << std::endl;
            return false;
        }
    }
    catch (const std::exception &e)
    {
        std::cerr << "登录失败: " << e.what() << std::endl;
        return false;
    }
}



void startCountdown(long total_seconds)
{
    is_running = true;
    countdown_thread = std::thread([total_seconds]()
                                   {
        long remaining = total_seconds;
        while (remaining > 0 && is_running) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            remaining--;
        }
        if (remaining <= 0) {
            std::cout << "时间到期，程序退出" << std::endl;
            exit(0);
        } });
    countdown_thread.detach();
}

#endif // CHECK_H